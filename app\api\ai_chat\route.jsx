import { chatSession } from '@/configs/AiModel';
import { NextResponse } from 'next/server';


export async function POST(req) {
    try {
        const {prompt, images} = await req.json();

        // Check if API key is available
        if (!process.env.NEXT_PUBLIC_GEMINI_API_KEY) {
            console.error('GEMINI API KEY is missing');
            return NextResponse.json({error: 'API key not configured'}, {status: 500});
        }

        console.log('Sending prompt to AI:', prompt?.substring(0, 100) + '...');

        let result;

        if (images && images.length > 0) {
            // Send multimodal content (text + images)
            const content = [prompt, ...images];
            result = await chatSession.sendMessage(content);
        } else {
            // Send text only
            result = await chatSession.sendMessage(prompt);
        }

        const AIresp = result.response.text();
        console.log('AI Response received:', AIresp?.substring(0, 100) + '...');
        return NextResponse.json({result: AIresp});
    } catch (error) {
        console.error('AI Chat API Error Details:', {
            message: error.message,
            stack: error.stack,
            name: error.name
        });
        return NextResponse.json({
            error: 'Failed to process AI request',
            details: error.message,
            type: error.name
        }, {status: 500});
    }
}